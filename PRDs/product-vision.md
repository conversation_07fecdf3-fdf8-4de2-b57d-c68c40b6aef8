# PRODUCT VISION

This web app is meant to be a place where users can keep track of their supermarket expenses via their purchase receipts. Users will be able to upload their receipts and the app will extract the relevant information from the receipt and store it in a database. We're unsure yet of the visualization format that the users will have to visualize their expenses. It might be a dashboard, it might be a chat with an AI that has access to its expenses, it might be a combination of both. We're not entirely sure of what are all the questions users will want to answer from this data, but a few of them are:

- How much did I spend on groceries last month?
- How much did my expenses grow/decrease in groceries over the last year?
- What are the most expensive items I buy?
- How much has the price of an item changed over time?
- What are the most expensive items I buy from a particular store?
- How much did I spend on groceries from a particular store last month?
- How much did I spend on groceries from a particular store over the last year?
- What are the most expensive stores I buy from?
- How much has the price of an item changed over time from a particular store?

Our database will have to be structured in a way that allows us to answer all of these questions. So far, we're only working with one store; we'll increase the number of stores we work with over time. For this particular store, the information that we have is a standard receipt that contains the following information:

- The store name
- The store address
  - The store city
  - The store state
  - The store zip code
- The store phone number
- The purchase date
- The purchase time
- The ticket number
- The purchase items
  - The item name
  - The item price
  - The item quantity
  - The item total price
- The purchase total
- The purchase tax
- The last 4 digits of the credit card used for the purchase
- The purchase payment method

## ITEM MATCHING STRATEGY

Since one of the questions we want to answer is about the evolution of the price of an item over time, we need to eventually identify when different receipt line items refer to the same product. Items may appear with slightly different names over time (e.g., "Tomato" vs "Tomato 1kg" vs "Organic Tomatoes"), but ideally we should group them as the same item for accurate price tracking and analytics.

### MVP Approach (Phase 1)

For the initial MVP, we will use a **simplified approach** to validate product-market fit without over-engineering the solution:

- **Direct Storage**: Store each receipt line item exactly as it appears on the receipt, without any matching or normalization
- **No Item Merging**: Each unique item name will be treated as a separate item in the database
- **Simple Analytics**: Price tracking and analytics will be based on exact string matches only

This means that "Tomato", "Tomato 1kg", and "Organic Tomatoes" will be tracked as three separate items initially. While this limits some analytics capabilities, it allows us to:

- Get the MVP to market quickly
- Validate core user value proposition
- Gather real user data to inform future matching strategies
- Avoid complex edge cases that could delay launch

### Database Design for Future Scalability

Even though we're using the simple approach for MVP, the database should be designed to support future smart matching capabilities without requiring major restructuring:

**Recommended Table Structure:**

- `receipt_items` table: Stores raw receipt line items exactly as they appear
- `master_items` table: Will eventually contain deduplicated/matched items (initially 1:1 with receipt_items)
- `item_matches` table: Will track relationships between receipt items and master items (initially simple direct matches)

This structure allows us to:

- Implement the MVP with simple logic (each receipt item creates one master item)
- Later add sophisticated matching algorithms without data migration
- Maintain audit trail of all matching decisions
- Support manual corrections and bulk operations in the future

Important note: the recommended table structure is only a recommendation. If there's an analysis or belief that more and/or different tables are convenient, we will add as many as necessary.

### Future Enhancement (Phase 2)

Once we validate PMF and have real user data, we plan to implement intelligent item matching using:

- AI-powered semantic matching for product equivalence
- Fuzzy string matching for minor variations
- User feedback mechanisms for manual corrections
- Learning algorithms that improve over time

The current database design will support this transition seamlessly, allowing us to retroactively apply smart matching to historical data.
