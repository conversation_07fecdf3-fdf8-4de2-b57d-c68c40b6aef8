# Sevalla Logging Debug Guide

## Problem: Empty Logs in Deployed App

If you're seeing empty logs in your Sevalla deployment, follow these debugging steps:

## ✅ **Updated Logging Features**

I've enhanced your logging to be more deployment-friendly:

1. **Multiple Output Streams**: Logs go to both stdout and stderr
2. **Dual Format**: Both JSON and plain text formats
3. **Immediate Flushing**: Forces log output immediately
4. **Startup Logging**: Logs fire immediately when app starts
5. **Test Endpoint**: `/test-logs` for debugging

## 🔍 **Debugging Steps**

### Step 1: Check if App is Starting
Visit your deployed app at: `https://your-app.sevalla.com/test-logs`

**Expected Response:**
```json
{
  "message": "Logs have been written - check your deployment logs",
  "timestamp": "2025-08-18T18:45:17.726Z",
  "logLevels": ["INFO", "WARN", "ERROR", "DEBUG"]
}
```

If you get this response, the app is running. Check Se<PERSON><PERSON> logs immediately after visiting this endpoint.

### Step 2: Check Sevalla Log Settings

1. **Log into Sevalla Dashboard**
2. **Go to your app → Logs section**
3. **Check log level settings** - ensure it's set to capture INFO level
4. **Check log retention** - make sure logs aren't being deleted too quickly
5. **Try different log views** - some platforms have separate stdout/stderr views

### Step 3: Check Build Process

Ensure your app is building correctly:
```bash
# In your local server directory
bun run build
```

If build fails, fix errors before deploying.

### Step 4: Check Port Configuration

Sevalla might require specific port configuration. Add this to your `package.json`:

```json
{
  "scripts": {
    "start": "bun run dist/index.js"
  }
}
```

Or ensure your app listens on the correct port:
```javascript
// Add this if needed
const port = process.env.PORT || 3000;
```

### Step 5: Force Error Logs

Visit: `https://your-app.sevalla.com/test-logs`

This endpoint specifically writes ERROR level logs which are more likely to be captured.

### Step 6: Check Sevalla Documentation

Different platforms have different logging requirements:
- Some only capture stderr
- Some require specific log formats
- Some have log level filtering
- Some have delayed log display

## 🚨 **Emergency Debugging**

If you still see no logs, add this simple debug endpoint:

```javascript
app.get("/debug", (c) => {
  // Multiple ways to output
  console.log("CONSOLE LOG TEST");
  console.error("CONSOLE ERROR TEST");
  process.stdout.write("STDOUT TEST\n");
  process.stderr.write("STDERR TEST\n");
  
  return c.text("Debug output sent - check logs");
});
```

## 📋 **Checklist**

- [ ] App responds to `/test-logs` endpoint
- [ ] Sevalla log level set to INFO or DEBUG
- [ ] Build process completes successfully
- [ ] Correct port configuration
- [ ] Checked both stdout and stderr logs in Sevalla
- [ ] Logs checked immediately after making requests

## 🔧 **Common Sevalla Issues**

1. **Log Buffering**: Logs might appear with delay
2. **Log Level Filtering**: Only ERROR logs might be shown by default
3. **Separate Log Streams**: stdout and stderr might be in different views
4. **Build Issues**: App might not be starting due to build failures

## 📞 **Next Steps**

1. Try the `/test-logs` endpoint first
2. Check Sevalla's specific logging documentation
3. Contact Sevalla support if logs still don't appear
4. Consider using external logging service (like LogTail, Papertrail) as backup

The enhanced logging should now work with most deployment platforms including Sevalla!
