# Mailgun Webhook Handler

## Overview

The `/handle-ticket-forwarding` endpoint processes POST requests from Mailgun email forwarding webhooks. It extracts email information, identifies PDF attachments, and parses their content with comprehensive logging for production debugging.

## Features

- ✅ **Email Data Extraction**: Extracts recipient, sender, subject, body content
- ✅ **PDF Attachment Detection**: Automatically identifies PDF files in email attachments
- ✅ **PDF Content Parsing**: Extracts text content from PDF attachments
- ✅ **Comprehensive Logging**: Detailed JSON logs for production debugging
- ✅ **Error Handling**: Graceful error handling with detailed error reporting
- ✅ **Performance Monitoring**: Request timing and processing metrics

## Endpoint Details

### URL
```
POST /handle-ticket-forwarding
```

### Expected Input
Mailgun webhook payload (typically `application/x-www-form-urlencoded` or `multipart/form-data`)

### Response Format
```json
{
  "success": true,
  "processingTimeMs": 115,
  "email": {
    "recipient": "<EMAIL>",
    "sender": "<EMAIL>",
    "from": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "Support Request with PDF Attachment",
    "bodyPlain": "Email body text...",
    "bodyHtml": "<p>Email body HTML...</p>",
    "attachmentCount": 1,
    "timestamp": "2025-08-18T18:44:35.491Z",
    "messageHeaders": "[[\"From\",\"<EMAIL>\"]...]"
  },
  "pdfAttachments": [
    {
      "filename": "document.pdf",
      "size": 465,
      "content": "Extracted PDF text content...",
      "success": true
    }
  ],
  "totalAttachments": 1
}
```

## Logging

All logs are output as structured JSON with the following format:
```json
{
  "timestamp": "2025-08-18T18:44:35.530Z",
  "level": "INFO|ERROR|WARN|DEBUG",
  "message": "Human readable message",
  "data": { /* Additional context data */ }
}
```

### Log Levels
- **INFO**: Important events (request received, processing completed, attachments found)
- **DEBUG**: Detailed processing steps (body parsing, attachment processing)
- **ERROR**: Errors and failures (PDF parsing failures, processing errors)
- **WARN**: Warnings (missing attachments, skipped files)

## Testing

Use the included test script to verify the implementation:

```bash
# Start the server
bun run dev

# Run the test (in another terminal)
SERVER_URL=http://localhost:3001 node test-mailgun-webhook.js
```

## Production Deployment

### Environment Variables
- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (set to 'production' for production)

### Monitoring
Monitor the following in production logs:
- Processing time (`processingTimeMs`)
- PDF parsing success rate
- Error patterns
- Request volume

### Mailgun Configuration
Configure Mailgun to forward emails to your endpoint:
```
https://your-domain.com/handle-ticket-forwarding
```

## Dependencies

- **hono**: Web framework
- **pdf-extraction**: PDF text extraction
- **@types/multer**: TypeScript types for file handling

## Error Handling

The endpoint handles various error scenarios:
- Invalid request format
- Missing email data
- PDF parsing failures
- Network timeouts
- Memory issues with large PDFs

All errors are logged with detailed context for debugging.

## Performance Considerations

- PDF parsing can be CPU intensive for large files
- Memory usage scales with PDF size
- Consider implementing file size limits for production
- Monitor processing times and set appropriate timeouts

## Security Notes

- Validate file types before processing
- Implement file size limits
- Consider virus scanning for production use
- Validate Mailgun webhook signatures in production
