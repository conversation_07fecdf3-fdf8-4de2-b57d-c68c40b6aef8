import type { CreateReceiptInput, CreateReceiptItemInput } from 'shared';

export interface ValidationError {
  field: string;
  message: string;
}

export class ValidationService {
  /**
   * Validate receipt data
   */
  static validateReceiptData(data: CreateReceiptInput): ValidationError[] {
    const errors: ValidationError[] = [];

    // Required fields
    if (!data.store_name || data.store_name.trim().length === 0) {
      errors.push({
        field: 'store_name',
        message: 'Store name is required'
      });
    }

    if (!data.purchase_date) {
      errors.push({
        field: 'purchase_date',
        message: 'Purchase date is required'
      });
    } else if (!this.isValidDate(data.purchase_date)) {
      errors.push({
        field: 'purchase_date',
        message: 'Purchase date must be in YYYY-MM-DD format'
      });
    }

    if (data.purchase_total === undefined || data.purchase_total === null) {
      errors.push({
        field: 'purchase_total',
        message: 'Purchase total is required'
      });
    } else if (data.purchase_total < 0) {
      errors.push({
        field: 'purchase_total',
        message: 'Purchase total must be non-negative'
      });
    } else if (!this.isValidCurrency(data.purchase_total)) {
      errors.push({
        field: 'purchase_total',
        message: 'Purchase total must be a valid currency amount (max 2 decimal places)'
      });
    }

    // Optional field validations
    if (data.purchase_time && !this.isValidTime(data.purchase_time)) {
      errors.push({
        field: 'purchase_time',
        message: 'Purchase time must be in HH:MM or HH:MM:SS format'
      });
    }

    if (data.purchase_tax !== undefined && data.purchase_tax !== null) {
      if (data.purchase_tax < 0) {
        errors.push({
          field: 'purchase_tax',
          message: 'Purchase tax must be non-negative'
        });
      } else if (!this.isValidCurrency(data.purchase_tax)) {
        errors.push({
          field: 'purchase_tax',
          message: 'Purchase tax must be a valid currency amount (max 2 decimal places)'
        });
      }
    }

    if (data.store_zip_code && !this.isValidZipCode(data.store_zip_code)) {
      errors.push({
        field: 'store_zip_code',
        message: 'Store zip code must be in valid format (12345 or 12345-6789)'
      });
    }

    if (data.store_phone_number && !this.isValidPhoneNumber(data.store_phone_number)) {
      errors.push({
        field: 'store_phone_number',
        message: 'Store phone number must be in valid format'
      });
    }

    if (data.credit_card_last_4 && !this.isValidCreditCardLast4(data.credit_card_last_4)) {
      errors.push({
        field: 'credit_card_last_4',
        message: 'Credit card last 4 digits must be exactly 4 digits'
      });
    }

    // String length validations
    if (data.store_name && data.store_name.length > 255) {
      errors.push({
        field: 'store_name',
        message: 'Store name must be 255 characters or less'
      });
    }

    if (data.store_address && data.store_address.length > 500) {
      errors.push({
        field: 'store_address',
        message: 'Store address must be 500 characters or less'
      });
    }

    if (data.ticket_number && data.ticket_number.length > 100) {
      errors.push({
        field: 'ticket_number',
        message: 'Ticket number must be 100 characters or less'
      });
    }

    return errors;
  }

  /**
   * Validate receipt item data
   */
  static validateReceiptItemData(data: CreateReceiptItemInput): ValidationError[] {
    const errors: ValidationError[] = [];

    // Required fields
    if (!data.receipt_id || data.receipt_id.trim().length === 0) {
      errors.push({
        field: 'receipt_id',
        message: 'Receipt ID is required'
      });
    }

    if (!data.item_name || data.item_name.trim().length === 0) {
      errors.push({
        field: 'item_name',
        message: 'Item name is required'
      });
    }

    if (data.item_price === undefined || data.item_price === null) {
      errors.push({
        field: 'item_price',
        message: 'Item price is required'
      });
    } else if (data.item_price < 0) {
      errors.push({
        field: 'item_price',
        message: 'Item price must be non-negative'
      });
    } else if (!this.isValidCurrency(data.item_price)) {
      errors.push({
        field: 'item_price',
        message: 'Item price must be a valid currency amount (max 2 decimal places)'
      });
    }

    if (data.item_quantity === undefined || data.item_quantity === null) {
      errors.push({
        field: 'item_quantity',
        message: 'Item quantity is required'
      });
    } else if (data.item_quantity <= 0) {
      errors.push({
        field: 'item_quantity',
        message: 'Item quantity must be positive'
      });
    } else if (!this.isValidQuantity(data.item_quantity)) {
      errors.push({
        field: 'item_quantity',
        message: 'Item quantity must be a valid number (max 3 decimal places)'
      });
    }

    if (data.item_total_price === undefined || data.item_total_price === null) {
      errors.push({
        field: 'item_total_price',
        message: 'Item total price is required'
      });
    } else if (data.item_total_price < 0) {
      errors.push({
        field: 'item_total_price',
        message: 'Item total price must be non-negative'
      });
    } else if (!this.isValidCurrency(data.item_total_price)) {
      errors.push({
        field: 'item_total_price',
        message: 'Item total price must be a valid currency amount (max 2 decimal places)'
      });
    }

    if (data.line_order === undefined || data.line_order === null) {
      errors.push({
        field: 'line_order',
        message: 'Line order is required'
      });
    } else if (!Number.isInteger(data.line_order) || data.line_order < 1) {
      errors.push({
        field: 'line_order',
        message: 'Line order must be a positive integer'
      });
    }

    // String length validations
    if (data.item_name && data.item_name.length > 255) {
      errors.push({
        field: 'item_name',
        message: 'Item name must be 255 characters or less'
      });
    }

    // Business logic validations
    if (data.item_price && data.item_quantity && data.item_total_price) {
      const expectedTotal = Math.round(data.item_price * data.item_quantity * 100) / 100;
      const actualTotal = Math.round(data.item_total_price * 100) / 100;
      
      // Allow for small rounding differences (1 cent)
      if (Math.abs(expectedTotal - actualTotal) > 0.01) {
        errors.push({
          field: 'item_total_price',
          message: `Item total price (${actualTotal}) doesn't match price × quantity (${expectedTotal})`
        });
      }
    }

    return errors;
  }

  /**
   * Validate multiple receipt items
   */
  static validateReceiptItems(items: CreateReceiptItemInput[]): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!items || items.length === 0) {
      errors.push({
        field: 'items',
        message: 'At least one receipt item is required'
      });
      return errors;
    }

    // Check for duplicate line orders
    const lineOrders = items.map(item => item.line_order);
    const duplicateOrders = lineOrders.filter((order, index) => lineOrders.indexOf(order) !== index);
    
    if (duplicateOrders.length > 0) {
      errors.push({
        field: 'items',
        message: `Duplicate line orders found: ${duplicateOrders.join(', ')}`
      });
    }

    // Validate each item
    items.forEach((item, index) => {
      const itemErrors = this.validateReceiptItemData(item);
      itemErrors.forEach(error => {
        errors.push({
          field: `items[${index}].${error.field}`,
          message: error.message
        });
      });
    });

    return errors;
  }

  // Helper validation methods
  private static isValidDate(dateString: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
      return false;
    }
    
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) && date.toISOString().startsWith(dateString);
  }

  private static isValidTime(timeString: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
    return timeRegex.test(timeString);
  }

  private static isValidCurrency(amount: number): boolean {
    // Check if the number has at most 2 decimal places
    return Number.isFinite(amount) && Math.round(amount * 100) === amount * 100;
  }

  private static isValidQuantity(quantity: number): boolean {
    // Check if the number has at most 3 decimal places
    return Number.isFinite(quantity) && Math.round(quantity * 1000) === quantity * 1000;
  }

  private static isValidZipCode(zipCode: string): boolean {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zipCode);
  }

  private static isValidPhoneNumber(phoneNumber: string): boolean {
    // Remove all non-digit characters
    const digitsOnly = phoneNumber.replace(/\D/g, '');
    // Check if it's 10 or 11 digits (with or without country code)
    return digitsOnly.length === 10 || digitsOnly.length === 11;
  }

  private static isValidCreditCardLast4(last4: string): boolean {
    const digitRegex = /^\d{4}$/;
    return digitRegex.test(last4);
  }
}
