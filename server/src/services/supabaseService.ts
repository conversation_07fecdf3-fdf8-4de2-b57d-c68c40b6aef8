import type { Context } from "hono";
import { getSupabase } from "../lib/middleware/supabase";
import type { Tables, Database } from "@shared/types/database";

type Receipt = Tables<"receipts">;
type ReceiptItem = Tables<"receipt_items">;

export const supabaseService = {
  async insertReceipt(
    c: Context,
    receipt: Database["public"]["Tables"]["receipts"]["Insert"]
  ) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipts")
      .insert(receipt)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to insert receipt: ${error.message}`);
    }

    return data;
  },

  async insertReceiptItems(
    c: Context,
    items: Database["public"]["Tables"]["receipt_items"]["Insert"][]
  ) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipt_items")
      .insert(items)
      .select();

    if (error) {
      throw new Error(`Failed to insert receipt items: ${error.message}`);
    }

    return data;
  },
  async getReceipt(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipts")
      .select("*")
      .eq("id", receiptId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch receipt: ${error.message}`);
    }

    return data;
  },
  async deleteReceipt(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    const { error } = await supabase
      .from("receipts")
      .delete()
      .eq("id", receiptId);

    if (error) {
      throw new Error(`Failed to delete receipt: ${error.message}`);
    }

    return;
  },
  async getAllReceiptsForUser(
    c: Context,
    userId?: string,
    limit = 50,
    offset = 0
  ) {
    const supabase = getSupabase(c);
    let query = supabase
      .from("receipts")
      .select("*")
      .order("purchase_date", { ascending: false })
      .range(offset, offset + limit - 1);

    // Add user filter if provided
    if (userId) {
      query = query.eq("user_id", userId);
    }

    const { data, error } = await query;
    if (error) {
      throw new Error(`Failed to fetch receipts: ${error.message}`);
    }

    return data;
  },

  async getReceiptWithItems(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    let query = supabase
      .from("receipts")
      .select(
        `
        *,
        receipt_items (*)
      `
      )
      .eq("id", receiptId);

    const { data, error } = await query.single();

    if (error) {
      if (error.code === "PGRST116") {
        return null; // Receipt not found
      }
      throw new Error(`Failed to fetch receipt: ${error.message}`);
    }

    return data;
  },

  async deleteReceiptById(c: Context, receiptId: string, userId?: string) {
    const supabase = getSupabase(c);
    let query = supabase.from("receipts").delete().eq("id", receiptId);

    // Add user filter if provided
    if (userId) {
      query = query.eq("user_id", userId);
    }

    const { error } = await query;

    if (error) {
      throw new Error(`Failed to delete receipt: ${error.message}`);
    }

    return true;
  },
  async getReceiptItems(c: Context, receiptId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("receipt_items")
      .select("*")
      .eq("receipt_id", receiptId)
      .order("line_order", { ascending: true });
    if (error) {
      throw new Error(`Failed to fetch receipt items: ${error.message}`);
    }

    return data;
  },
  async getMasterItem(c: Context, masterItemId: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("master_items")
      .select("*")
      .eq("id", masterItemId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch master item: ${error.message}`);
    }

    return data;
  },
  async insertMasterItem(
    c: Context,
    masterItem: Database["public"]["Tables"]["master_items"]["Insert"]
  ) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("master_items")
      .insert(masterItem)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to insert master item: ${error.message}`);
    }

    return data;
  },

  async getMasterItemByName(c: Context, normalizedName: string) {
    const supabase = getSupabase(c);
    const { data, error } = await supabase
      .from("master_items")
      .select("id")
      .eq("normalized_name", normalizedName)
      .single();

    if (error && error.code !== "PGRST116") {
      throw new Error(`Failed to fetch master item: ${error.message}`);
    }

    return data;
  },

  async insertItemMatch(
    c: Context,
    itemMatch: Database["public"]["Tables"]["item_matches"]["Insert"]
  ) {
    const supabase = getSupabase(c);
    const { error } = await supabase.from("item_matches").insert(itemMatch);

    if (error) {
      throw new Error(`Failed to insert item match: ${error.message}`);
    }

    return true;
  },
};
