import type { Context } from "hono";
import { ValidationService } from "../utils/validation";
import { supabaseService } from "./supabaseService";
import type {
  Receipt,
  ReceiptItem,
  CreateReceiptInput,
  CreateReceiptItemInput,
  ReceiptWithItems,
  MasterItem,
  ItemMatch,
} from "shared";

export class ReceiptService {
  /**
   * Create a new receipt with its items
   */
  async createReceiptWithItems(
    c: Context,
    receiptData: CreateReceiptInput,
    items: Omit<CreateReceiptItemInput, "receipt_id">[]
  ): Promise<{ receipt: Receipt; items: ReceiptItem[] }> {
    try {
      // Validate receipt data
      const receiptErrors = ValidationService.validateReceiptData(receiptData);
      if (receiptErrors.length > 0) {
        throw new Error(
          `Receipt validation failed: ${receiptErrors.map((e) => `${e.field}: ${e.message}`).join(", ")}`
        );
      }

      // Validate receipt items (add temporary receipt_id for validation)
      const itemsWithTempId = items.map((item, index) => ({
        ...item,
        receipt_id: "temp-id",
        line_order: item.line_order || index + 1,
      }));

      const itemErrors =
        ValidationService.validateReceiptItems(itemsWithTempId);
      if (itemErrors.length > 0) {
        throw new Error(
          `Receipt items validation failed: ${itemErrors.map((e) => `${e.field}: ${e.message}`).join(", ")}`
        );
      }
      // Start a transaction by creating the receipt first
      const receipt = await supabaseService.insertReceipt(c, {
        user_id: receiptData.user_id || null, // For now, allow null user_id
        store_name: receiptData.store_name,
        store_address: receiptData.store_address || null,
        store_city: receiptData.store_city || null,
        store_state: receiptData.store_state || null,
        store_zip_code: receiptData.store_zip_code || null,
        store_phone_number: receiptData.store_phone_number || null,
        purchase_date: receiptData.purchase_date,
        purchase_time: receiptData.purchase_time || null,
        ticket_number: receiptData.ticket_number || null,
        purchase_total: receiptData.purchase_total,
        purchase_tax: receiptData.purchase_tax || null,
        credit_card_last_4: receiptData.credit_card_last_4 || null,
        payment_method: receiptData.payment_method || null,
        raw_pdf_text: receiptData.raw_pdf_text || null,
      });

      if (!receipt) {
        throw new Error("Receipt was not created");
      }

      // Create receipt items
      const itemsToInsert = items.map((item) => ({
        receipt_id: receipt.id,
        item_name: item.item_name,
        item_price: item.item_price,
        item_quantity: item.item_quantity,
        item_total_price: item.item_total_price,
        line_order: item.line_order,
      }));

      const createdItems = await supabaseService.insertReceiptItems(
        c,
        itemsToInsert
      );

      if (!createdItems) {
        throw new Error("Receipt items were not created");
      }

      // Create master items and matches for MVP (1:1 relationship)
      await this.createMasterItemsForReceiptItems(c, createdItems);

      return {
        receipt: receipt as Receipt,
        items: createdItems as ReceiptItem[],
      };
    } catch (error) {
      throw new Error(
        `Receipt creation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Create master items and matches for receipt items (MVP approach)
   */
  private async createMasterItemsForReceiptItems(
    c: Context,
    receiptItems: ReceiptItem[]
  ): Promise<void> {
    for (const item of receiptItems) {
      try {
        // Check if master item already exists
        const existingMasterItem = await supabaseService.getMasterItemByName(
          c,
          item.item_name.toLowerCase().trim()
        );

        let masterItemId: string;

        if (existingMasterItem) {
          masterItemId = existingMasterItem.id;
        } else {
          // Create new master item
          try {
            const newMasterItem = await supabaseService.insertMasterItem(c, {
              normalized_name: item.item_name.toLowerCase().trim(),
              category: null, // Will be added in future phases
            });

            masterItemId = newMasterItem.id;
          } catch (masterItemError) {
            console.error(
              `Failed to create master item for ${item.item_name}:`,
              masterItemError
            );
            continue; // Skip this item match if master item creation fails
          }
        }

        // Create item match
        try {
          await supabaseService.insertItemMatch(c, {
            receipt_item_id: item.id,
            master_item_id: masterItemId,
            confidence_score: 1.0, // Perfect match for MVP
            match_method: "exact",
          });
        } catch (matchError) {
          console.error(
            `Failed to create item match for ${item.item_name}:`,
            matchError
          );
        }
      } catch (error) {
        console.error(
          `Error processing master item for ${item.item_name}:`,
          error
        );
      }
    }
  }

  /**
   * Get a receipt by ID with its items
   */
  async getReceiptWithItems(
    c: Context,
    receiptId: string
  ): Promise<ReceiptWithItems | null> {
    try {
      const data = await supabaseService.getReceiptWithItems(c, receiptId);
      return data as ReceiptWithItems;
    } catch (error) {
      throw new Error(
        `Failed to get receipt: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get all receipts for a user
   */
  async getUserReceipts(
    c: Context,
    userId?: string,
    limit = 50,
    offset = 0
  ): Promise<Receipt[]> {
    try {
      const data = await supabaseService.getAllReceiptsForUser(
        c,
        userId,
        limit,
        offset
      );
      return (data || []) as Receipt[];
    } catch (error) {
      throw new Error(
        `Failed to get user receipts: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Delete a receipt and all its items
   */
  async deleteReceipt(
    c: Context,
    receiptId: string,
    userId?: string
  ): Promise<boolean> {
    try {
      return await supabaseService.deleteReceiptById(c, receiptId, userId);
    } catch (error) {
      throw new Error(
        `Failed to delete receipt: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get receipt items for a specific receipt
   */
  async getReceiptItems(c: Context, receiptId: string): Promise<ReceiptItem[]> {
    try {
      const data = await supabaseService.getReceiptItems(c, receiptId);
      return (data || []) as ReceiptItem[];
    } catch (error) {
      throw new Error(
        `Failed to get receipt items: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}
