import type { ParsedReceiptData } from "shared";

export class ReceiptParser {
  /**
   * Parse receipt text and extract structured data
   */
  static parseReceiptText(pdfText: string): ParsedReceiptData {
    const lines = pdfText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    const result: ParsedReceiptData = {
      store_name: "",
      purchase_date: "",
      purchase_total: 0,
      items: [],
      raw_pdf_text: pdfText,
    };

    try {
      // Extract store information
      const storeInfo = this.extractStoreInfo(lines);
      Object.assign(result, storeInfo);

      // Extract transaction information
      const transactionInfo = this.extractTransactionInfo(lines);
      Object.assign(result, transactionInfo);

      // Extract financial information
      const financialInfo = this.extractFinancialInfo(lines);
      Object.assign(result, financialInfo);

      // Extract line items
      result.items = this.extractItems(lines);

      return result;
    } catch (error) {
      console.error("Error parsing receipt:", error);
      return result;
    }
  }

  /**
   * Extract store information from receipt lines
   */
  private static extractStoreInfo(lines: string[]): Partial<ParsedReceiptData> {
    const result: Partial<ParsedReceiptData> = {};

    // Store name is typically the first line, but remove tax ID
    if (lines.length > 0 && lines[0]) {
      // Remove tax ID pattern like "A-46103834"
      result.store_name = lines[0].replace(/\s+[A-Z]-\d+$/, "");
    }

    // Address is typically the second line
    if (lines.length > 1 && lines[1]) {
      result.store_address = lines[1];
    }

    // City and zip code are on the third line (format: "46015 VALENCIA")
    if (lines.length > 2 && lines[2]) {
      const cityZipMatch = lines[2].match(/^(\d{5})\s+(.+)$/);
      if (cityZipMatch && cityZipMatch[1] && cityZipMatch[2]) {
        result.store_zip_code = cityZipMatch[1];
        result.store_city = cityZipMatch[2];
      }
    }

    // Phone number (format: "TELÉFONO:*********")
    for (const line of lines) {
      const phoneMatch = line.match(/TELÉFONO:(\d+)/);
      if (phoneMatch) {
        result.store_phone_number = phoneMatch[1];
        break;
      }
    }

    return result;
  }

  /**
   * Extract transaction information (date, time, ticket number)
   */
  private static extractTransactionInfo(
    lines: string[]
  ): Partial<ParsedReceiptData> {
    const result: Partial<ParsedReceiptData> = {};

    for (const line of lines) {
      // Date and time (format: "10/09/2024 12:47  OP: 4089240")
      const dateTimeMatch = line.match(/(\d{2}\/\d{2}\/\d{4})\s+(\d{2}:\d{2})/);
      if (dateTimeMatch) {
        result.purchase_date = dateTimeMatch[1];
        result.purchase_time = dateTimeMatch[2];
      }

      // Ticket number (format: "FACTURA SIMPLIFICADA: 2786-**********")
      const ticketMatch = line.match(/FACTURA SIMPLIFICADA:\s*(.+)/);
      if (ticketMatch) {
        result.ticket_number = ticketMatch[1];
      }
    }

    return result;
  }

  /**
   * Extract financial information (total, tax, payment method)
   */
  private static extractFinancialInfo(
    lines: string[]
  ): Partial<ParsedReceiptData> {
    const result: Partial<ParsedReceiptData> = {};

    for (const line of lines) {
      // Total amount (format: "TOTAL (€)14,95")
      const totalMatch = line.match(/TOTAL\s*\(€\)([0-9,]+)/);
      if (totalMatch && totalMatch[1]) {
        result.purchase_total = this.parsePrice(totalMatch[1]);
      }

      // Credit card last 4 digits (format: "TARJ. BANCARIA:  **** **** **** 3642")
      const cardMatch = line.match(/TARJ\.\s*BANCARIA:.*?(\d{4})\s*$/);
      if (cardMatch) {
        result.credit_card_last_4 = cardMatch[1];
      }
    }

    // Calculate tax from IVA section
    result.purchase_tax = this.extractTaxAmount(lines);

    return result;
  }

  /**
   * Extract tax amount by summing all CUOTA values from IVA section
   */
  private static extractTaxAmount(lines: string[]): number {
    let totalTax = 0;

    for (const line of lines) {
      // Look for IVA CUOTA values in format like "10%4,050,40" or "21%8,681,82"
      // The pattern is: percentage%base_amount,tax_amount
      // Both amounts use comma as decimal separator
      // "10%4,050,40" means base=4.05, tax=0.40
      // "21%8,681,82" means base=8.68, tax=1.82
      const taxMatch = line.match(/(\d+)%(\d+,\d+)(\d+,\d+)$/);
      if (taxMatch && taxMatch[3]) {
        // The third capture group is the tax amount
        const taxAmount = this.parsePrice(taxMatch[3]);
        totalTax += taxAmount;
      }
    }

    return Math.round(totalTax * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Parse price string with comma decimal separator to number
   */
  private static parsePrice(priceStr: string): number {
    return parseFloat(priceStr.replace(",", "."));
  }

  /**
   * Extract line items from the receipt
   */
  private static extractItems(lines: string[]): Array<{
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
    line_order: number;
  }> {
    const items: Array<{
      item_name: string;
      item_price: number;
      item_quantity: number;
      item_total_price: number;
      line_order: number;
    }> = [];

    // Find the items section (between "DescripciónP. UnitImporte" and "TOTAL (€)")
    const startIndex = lines.findIndex((line) =>
      line.includes("DescripciónP. UnitImporte")
    );
    const endIndex = lines.findIndex((line) => line.includes("TOTAL (€)"));

    if (startIndex === -1 || endIndex === -1) {
      return items;
    }

    let lineOrder = 1;
    let i = startIndex + 1;

    while (i < endIndex) {
      const line = lines[i];

      // Skip empty lines or lines that don't look like items
      if (!line || line.length < 3) {
        i++;
        continue;
      }

      const item = this.parseItemLine(line, lines, i);
      if (item) {
        const { isWeightBased, ...itemData } = item;
        items.push({
          ...itemData,
          line_order: lineOrder++,
        });

        // If it was a weight-based item, skip the next line
        if (isWeightBased) {
          i += 2;
        } else {
          i++;
        }
      } else {
        i++;
      }
    }

    return items;
  }

  /**
   * Parse a single item line and determine its pattern
   */
  private static parseItemLine(
    line: string,
    lines: string[],
    currentIndex: number
  ): {
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
    isWeightBased?: boolean;
  } | null {
    // Check if this is a weight-based item (next line contains kg and €/kg)
    const nextLine = lines[currentIndex + 1];
    if (nextLine && nextLine.includes("kg") && nextLine.includes("€/kg")) {
      return this.parseWeightBasedItem(line, nextLine);
    }

    // Try to parse as regular item
    return this.parseRegularItem(line);
  }

  /**
   * Parse weight-based items that span two lines
   * Example:
   * Line 1: "1PLATANO"
   * Line 2: "0,914 kg2,95 €/kg2,70"
   */
  private static parseWeightBasedItem(
    nameLine: string,
    weightLine: string
  ): {
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
    isWeightBased: boolean;
  } | null {
    // Extract item name from first line (remove leading quantity)
    const nameMatch = nameLine.match(/^\d+(.+)$/);
    if (!nameMatch || !nameMatch[1]) return null;

    const itemName = nameMatch[1];

    // Parse weight line: "0,914 kg2,95 €/kg2,70"
    const weightMatch = weightLine.match(
      /([0-9,]+)\s*kg([0-9,]+)\s*€\/kg([0-9,]+)$/
    );
    if (!weightMatch || !weightMatch[1] || !weightMatch[2] || !weightMatch[3])
      return null;

    const quantity = this.parsePrice(weightMatch[1]);
    const unitPrice = this.parsePrice(weightMatch[2]);
    const totalPrice = this.parsePrice(weightMatch[3]);

    return {
      item_name: itemName,
      item_price: unitPrice,
      item_quantity: quantity,
      item_total_price: totalPrice,
      isWeightBased: true,
    };
  }

  /**
   * Parse regular items (single line)
   * Pattern 1: "1TOM. RECETA ARTESANA2,20" (quantity + name + price)
   * Pattern 2: "2PERLAS SILICE GATO3,557,10" (quantity + name + unit_price + total_price)
   */
  private static parseRegularItem(line: string): {
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
  } | null {
    // First, try to match pattern with two prices
    // Example: "2PERLAS SILICE GATO3,557,10"
    // This should be parsed as: qty=2, name="PERLAS SILICE GATO", unit=3.55, total=7.10

    // Look for pattern: number + text + number,number + number,number
    const twoPriceMatch = line.match(/^(\d+)(.+?)(\d+,\d+)(\d+,\d+)$/);
    if (
      twoPriceMatch &&
      twoPriceMatch[1] &&
      twoPriceMatch[2] &&
      twoPriceMatch[3] &&
      twoPriceMatch[4]
    ) {
      const quantity = parseInt(twoPriceMatch[1]);
      const itemName = twoPriceMatch[2];
      const unitPrice = this.parsePrice(twoPriceMatch[3]);
      const totalPrice = this.parsePrice(twoPriceMatch[4]);

      // Validate that quantity * unitPrice ≈ totalPrice (allowing for small rounding differences)
      const expectedTotal = quantity * unitPrice;
      if (Math.abs(expectedTotal - totalPrice) < 0.01) {
        return {
          item_name: itemName,
          item_price: unitPrice,
          item_quantity: quantity,
          item_total_price: totalPrice,
        };
      }
    }

    // If two-price pattern doesn't work, try single price pattern
    // Handle special cases like "1CERV PACK 126,96" which should be "CERV PACK 12" with price 6.96
    const singlePriceMatch = line.match(/^(\d+)(.+?)(\d+,\d+)$/);
    if (
      singlePriceMatch &&
      singlePriceMatch[1] &&
      singlePriceMatch[2] &&
      singlePriceMatch[3]
    ) {
      const quantity = parseInt(singlePriceMatch[1]);
      let itemName = singlePriceMatch[2];
      const priceStr = singlePriceMatch[3];

      // Special handling for cases like "CERV PACK 126,96"
      // where the last digits before comma might be part of the name
      if (priceStr.length > 4) {
        // e.g., "126,96" has length 6
        // Extract the last 2 digits before comma as part of name
        const priceMatch = priceStr.match(/^(\d+)(\d,\d+)$/);
        if (priceMatch && priceMatch[1] && priceMatch[2]) {
          itemName = itemName + priceMatch[1];
          const price = this.parsePrice(priceMatch[2]);

          return {
            item_name: itemName,
            item_price: price,
            item_quantity: quantity,
            item_total_price: price,
          };
        }
      }

      // Normal single price case
      const price = this.parsePrice(priceStr);
      return {
        item_name: itemName,
        item_price: price,
        item_quantity: quantity,
        item_total_price: price,
      };
    }

    return null;
  }
}
