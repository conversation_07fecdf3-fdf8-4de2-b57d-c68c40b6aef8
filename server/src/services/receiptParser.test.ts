import { describe, test, expect } from "bun:test";
import { ReceiptParser } from "./receiptParser";
import type { ParsedReceiptData } from "shared";

describe("ReceiptParser", () => {
  describe("parseReceiptText", () => {
    test("should parse a mercadona receipt: 2786-**********", () => {
      const sampleReceiptText = `\n\nMERCADONA, S.A.   A-46103834\nC/ DOCTOR NICASIO BENLLOCH 72\n46015 VALENCIA\nTELÉFONO:*********\n10/09/2024 12:47  OP: 4089240\nFACTURA SIMPLIFICADA: 2786-**********\nDescripciónP. UnitImporte\n1TOM. RECETA ARTESANA2,20\n2PERLAS SILICE GATO3,557,10\n1SOJA TEXTURIZADA2,25\n2TAPONES OÍDOS ESPUMA1,703,40\nTOTAL (€)14,95\nTARJETA BANCARIA14,95\nIVABASE IMPONIBLE (€)CUOTA (€)\n10%4,050,40\n21%8,681,82\nTOTAL12,732,22\nTARJ. BANCARIA:  **** **** **** 3642\nN.C: 098101991 AUT: POBMZ2\nAID: A0000000031010ARC: 00\nVerificado por dispositivo\nImporte: 14,95 €Visa DEBIT\nSE ADMITEN DEVOLUCIONES CON TICKET`;

      const result = ReceiptParser.parseReceiptText(sampleReceiptText);

      expect(result).toEqual({
        store_name: "MERCADONA, S.A.",
        store_address: "C/ DOCTOR NICASIO BENLLOCH 72",
        store_city: "VALENCIA",
        store_zip_code: "46015",
        store_phone_number: "*********",
        purchase_date: "10/09/2024",
        purchase_time: "12:47",
        ticket_number: "2786-**********",
        purchase_total: 14.95,
        purchase_tax: 2.22,
        credit_card_last_4: "3642",
        //payment_method: "visa",
        items: [
          {
            item_name: "TOM. RECETA ARTESANA",
            item_price: 2.2,
            item_quantity: 1,
            item_total_price: 2.2,
            line_order: 1,
          },
          {
            item_name: "PERLAS SILICE GATO",
            item_price: 3.55,
            item_quantity: 2,
            item_total_price: 7.1,
            line_order: 2,
          },
          {
            item_name: "SOJA TEXTURIZADA",
            item_price: 2.25,
            item_quantity: 1,
            item_total_price: 2.25,
            line_order: 3,
          },
          {
            item_name: "TAPONES OÍDOS ESPUMA",
            item_price: 1.7,
            item_quantity: 2,
            item_total_price: 3.4,
            line_order: 4,
          },
        ],
        raw_pdf_text: sampleReceiptText,
      });
    });

    test("should parse a mercadona receipt: 2786-**********", () => {
      const sampleReceiptText = `\n\nMERCADONA, S.A.   A-46103834\nC/ DOCTOR NICASIO BENLLOCH 72\n46015 VALENCIA\nTELÉFONO:*********\n27/03/2025 21:04  OP: 699017\nFACTURA SIMPLIFICADA: 2786-**********\nDescripciónP. UnitImporte\n1SUPERSANDWICH NATA3,30\n1TARTA NATA CHOCOLATE2,00\n1BARRITA RELLENA LECH2,20\n1CHO LECHE AVELLANA T1,50\n1SALSA PESTO FRESCO2,00\n1PLATANO\n0,914 kg2,95 €/kg2,70\nTOTAL (€)13,70\nTARJETA BANCARIA13,70\nIVABASE IMPONIBLE (€)CUOTA (€)\n4%2,600,10\n10%10,001,00\nTOTAL12,601,10\nTARJ. BANCARIA:  **** **** **** 3642\nN.C: 098101991 AUT: V8FJLE\nAID: A0000000031010ARC: 00\nVerificado por dispositivo\nImporte: 13,70 €Visa DEBIT\nSE ADMITEN DEVOLUCIONES CON TICKET`;

      const result = ReceiptParser.parseReceiptText(sampleReceiptText);

      expect(result).toEqual({
        store_name: "MERCADONA, S.A.",
        store_address: "C/ DOCTOR NICASIO BENLLOCH 72",
        store_city: "VALENCIA",
        store_zip_code: "46015",
        store_phone_number: "*********",
        purchase_date: "27/03/2025",
        purchase_time: "21:04",
        ticket_number: "2786-**********",
        purchase_total: 13.7,
        purchase_tax: 1.1,
        credit_card_last_4: "3642",
        //payment_method: "visa",
        items: [
          {
            item_name: "SUPERSANDWICH NATA",
            item_price: 3.3,
            item_quantity: 1,
            item_total_price: 3.3,
            line_order: 1,
          },
          {
            item_name: "TARTA NATA CHOCOLATE",
            item_price: 2,
            item_quantity: 1,
            item_total_price: 2,
            line_order: 2,
          },
          {
            item_name: "BARRITA RELLENA LECH",
            item_price: 2.2,
            item_quantity: 1,
            item_total_price: 2.2,
            line_order: 3,
          },
          {
            item_name: "CHO LECHE AVELLANA T",
            item_price: 1.5,
            item_quantity: 1,
            item_total_price: 1.5,
            line_order: 4,
          },
          {
            item_name: "SALSA PESTO FRESCO",
            item_price: 2,
            item_quantity: 1,
            item_total_price: 2,
            line_order: 5,
          },
          {
            item_name: "PLATANO",
            item_price: 2.95,
            item_quantity: 0.914,
            item_total_price: 2.7,
            line_order: 6,
          },
        ],
        raw_pdf_text: sampleReceiptText,
      });
    });

    test("should parse a mercadona receipt: 2786-**********", () => {
      const sampleReceiptText = `\n\nMERCADONA, S.A.   A-46103834\nC/ DOCTOR NICASIO BENLLOCH 72\n46015 VALENCIA\nTELÉFONO:*********\n30/03/2024 18:39  OP: 69901\nFACTURA SIMPLIFICADA: 2786-**********\nDescripciónP. UnitImporte\n1CERV PACK 126,96\n1BARRITA RELLENA LECH1,95\nTOTAL (€)8,91\nTARJETA BANCARIA8,91\nIVABASE IMPONIBLE (€)CUOTA (€)\n10%1,770,18\n21%5,751,21\nTOTAL7,521,39\nTARJ. BANCARIA:  **** **** **** 4300\nN.C: 098101991 AUT: NNQNCY\nAID: A0000000031010  ARC: 3030\nVerificado por dispositivo\nImporte: 8,91 € Visa DEBIT\nSE ADMITEN DEVOLUCIONES CON TICKET`;

      const result = ReceiptParser.parseReceiptText(sampleReceiptText);

      expect(result).toEqual({
        store_name: "MERCADONA, S.A.",
        store_address: "C/ DOCTOR NICASIO BENLLOCH 72",
        store_city: "VALENCIA",
        store_zip_code: "46015",
        store_phone_number: "*********",
        purchase_date: "30/03/2024",
        purchase_time: "18:39",
        ticket_number: "2786-**********",
        purchase_total: 8.91,
        purchase_tax: 1.39,
        credit_card_last_4: "4300",
        //payment_method: "visa",
        items: [
          {
            item_name: "CERV PACK 12",
            item_price: 6.96,
            item_quantity: 1,
            item_total_price: 6.96,
            line_order: 1,
          },
          {
            item_name: "BARRITA RELLENA LECH",
            item_price: 1.95,
            item_quantity: 1,
            item_total_price: 1.95,
            line_order: 2,
          },
        ],
        raw_pdf_text: sampleReceiptText,
      });
    });

    test("should parse a mercadona receipt: 2786-**********", () => {
      const sampleReceiptText = `\n\nMERCADONA, S.A.   A-46103834\nC/ DOCTOR NICASIO BENLLOCH 72\n46015 VALENCIA\nTELÉFONO:*********\n18/03/2025 13:50  OP: 676181\nFACTURA SIMPLIFICADA: 2786-**********\nDescripciónP. UnitImporte\n2PERLAS SILICE GATO3,557,10\n2MEDIALUNAS CALABAZA2,204,40\n2MEDIALUNAS PARMIG.2,605,20\n1CINTAS DE BACON2,30\n1RELLENO KEBAB POLLO3,25\n1ÑOQUIS POLLO/VERDURA3,15\n1QUESO GRANA PADANO3,82\n1MOZZARELLA FRESCA0,97\n1PLATANO\n1,256 kg2,95 €/kg3,71\nTOTAL (€)33,90\nTARJETA BANCARIA33,90\nIVABASE IMPONIBLE (€)CUOTA (€)\n4%8,170,33\n10%16,641,66\n21%5,871,23\nTOTAL30,683,22\nTARJ. BANCARIA:  **** **** **** 3642\nN.C: 098101991 AUT: FRJTLS\nAID: A0000000031010ARC: 00\nVerificado por dispositivo\nImporte: 33,90 €Visa DEBIT\nSE ADMITEN DEVOLUCIONES CON TICKET`;

      const result = ReceiptParser.parseReceiptText(sampleReceiptText);

      expect(result).toEqual({
        store_name: "MERCADONA, S.A.",
        store_address: "C/ DOCTOR NICASIO BENLLOCH 72",
        store_city: "VALENCIA",
        store_zip_code: "46015",
        store_phone_number: "*********",
        purchase_date: "18/03/2025",
        purchase_time: "13:50",
        ticket_number: "2786-**********",
        purchase_total: 33.9,
        purchase_tax: 3.22,
        credit_card_last_4: "3642",
        //payment_method: "visa",
        items: [
          {
            item_name: "PERLAS SILICE GATO",
            item_price: 3.55,
            item_quantity: 2,
            item_total_price: 7.1,
            line_order: 1,
          },
          {
            item_name: "MEDIALUNAS CALABAZA",
            item_price: 2.2,
            item_quantity: 2,
            item_total_price: 4.4,
            line_order: 2,
          },
          {
            item_name: "MEDIALUNAS PARMIG.",
            item_price: 2.6,
            item_quantity: 2,
            item_total_price: 5.2,
            line_order: 3,
          },
          {
            item_name: "CINTAS DE BACON",
            item_price: 2.3,
            item_quantity: 1,
            item_total_price: 2.3,
            line_order: 4,
          },
          {
            item_name: "RELLENO KEBAB POLLO",
            item_price: 3.25,
            item_quantity: 1,
            item_total_price: 3.25,
            line_order: 5,
          },
          {
            item_name: "ÑOQUIS POLLO/VERDURA",
            item_price: 3.15,
            item_quantity: 1,
            item_total_price: 3.15,
            line_order: 6,
          },
          {
            item_name: "QUESO GRANA PADANO",
            item_price: 3.82,
            item_quantity: 1,
            item_total_price: 3.82,
            line_order: 7,
          },
          {
            item_name: "MOZZARELLA FRESCA",
            item_price: 0.97,
            item_quantity: 1,
            item_total_price: 0.97,
            line_order: 8,
          },
          {
            item_name: "PLATANO",
            item_price: 2.95,
            item_quantity: 1.256,
            item_total_price: 3.71,
            line_order: 9,
          },
        ],
        raw_pdf_text: sampleReceiptText,
      });
    });

    test("should parse a mercadona receipt: 2516-**********", () => {
      const sampleReceiptText = `\n\nMERCADONA, S.A.   A-46103834\nC/ EMILIO BARÓ 23\n46020 VALENCIA\nTELÉFONO:*********\n15/12/2023 20:31  OP: 35912\nFACTURA SIMPLIFICADA: 2516-**********\nDescripciónP. UnitImporte\n1GRIEGO NATURAL2,45\n1BOLSA PLASTICO0,15\n2NATA COCINAR FRESCA1,202,40\n1GOLOSINAS MIX PICA1,65\n1SALCHICHÓN IB. 4PACK2,03\n1MINI SALADAS 24 UN2,50\n1EMPANADA ATUN3,90\n1PIZZA DEEP PEPPERONI3,50\n1PIZZA FORMAGGI4,00\n1PATATAS SERRANO1,20\n1CAFÉ NATURAL HOGAR3,55\n1YOGOMIX NATURAL BOLI1,20\n1PAPEL HIGIENICO 4 CA3,40\n1PARKING4,16\nENTRADA  17:39       SALIDA  20:31\nTOTAL (€)36,09\nTARJETA BANCARIA36,09\nIVABASE IMPONIBLE (€)CUOTA (€)\n10%25,802,58\n21%6,371,34\nTOTAL32,173,92\nTARJ. BANCARIA:  **** **** **** 4300\nN.C: 098100696 AUT: MCCEBF\nAID: A0000000031010  ARC: 3030\nVerificado por dispositivo\nImporte: 36,09 €Visa DEBIT\nSE ADMITEN DEVOLUCIONES CON TICKET\nDISPONE DE 20 MINUTOS\nPARA RETIRAR SU VEHÍCULO`;

      const result = ReceiptParser.parseReceiptText(sampleReceiptText);

      expect(result).toEqual({
        store_name: "MERCADONA, S.A.",
        store_address: "C/ EMILIO BARÓ 23",
        store_city: "VALENCIA",
        store_zip_code: "46020",
        store_phone_number: "*********",
        purchase_date: "15/12/2023",
        purchase_time: "20:31",
        ticket_number: "2516-**********",
        purchase_total: 36.09,
        purchase_tax: 3.92,
        credit_card_last_4: "4300",
        //payment_method: "visa",
        items: [
          {
            item_name: "GRIEGO NATURAL",
            item_price: 2.45,
            item_quantity: 1,
            item_total_price: 2.45,
            line_order: 1,
          },
          {
            item_name: "BOLSA PLASTICO",
            item_price: 0.15,
            item_quantity: 1,
            item_total_price: 0.15,
            line_order: 2,
          },
          {
            item_name: "NATA COCINAR FRESCA",
            item_price: 1.2,
            item_quantity: 2,
            item_total_price: 2.4,
            line_order: 3,
          },
          {
            item_name: "GOLOSINAS MIX PICA",
            item_price: 1.65,
            item_quantity: 1,
            item_total_price: 1.65,
            line_order: 4,
          },
          {
            item_name: "SALCHICHÓN IB. 4PACK",
            item_price: 2.03,
            item_quantity: 1,
            item_total_price: 2.03,
            line_order: 5,
          },
          {
            item_name: "MINI SALADAS 24 UN",
            item_price: 2.5,
            item_quantity: 1,
            item_total_price: 2.5,
            line_order: 6,
          },
          {
            item_name: "EMPANADA ATUN",
            item_price: 3.9,
            item_quantity: 1,
            item_total_price: 3.9,
            line_order: 7,
          },
          {
            item_name: "PIZZA DEEP PEPPERONI",
            item_price: 3.5,
            item_quantity: 1,
            item_total_price: 3.5,
            line_order: 8,
          },
          {
            item_name: "PIZZA FORMAGGI",
            item_price: 4,
            item_quantity: 1,
            item_total_price: 4,
            line_order: 9,
          },
          {
            item_name: "PATATAS SERRANO",
            item_price: 1.2,
            item_quantity: 1,
            item_total_price: 1.2,
            line_order: 10,
          },
          {
            item_name: "CAFÉ NATURAL HOGAR",
            item_price: 3.55,
            item_quantity: 1,
            item_total_price: 3.55,
            line_order: 11,
          },
          {
            item_name: "YOGOMIX NATURAL BOLI",
            item_price: 1.2,
            item_quantity: 1,
            item_total_price: 1.2,
            line_order: 12,
          },
          {
            item_name: "PAPEL HIGIENICO 4 CA",
            item_price: 3.4,
            item_quantity: 1,
            item_total_price: 3.4,
            line_order: 13,
          },
          {
            item_name: "PARKING",
            item_price: 4.16,
            item_quantity: 1,
            item_total_price: 4.16,
            line_order: 14,
          },
        ],
        raw_pdf_text: sampleReceiptText,
      });
    });

    test("should handle receipt with cash payment", () => {
      expect(true).toBe(true);
    });

    test("should preserve raw PDF text", () => {
      const sampleReceiptText = "Any receipt content";
      const result = ReceiptParser.parseReceiptText(sampleReceiptText);

      expect(result.raw_pdf_text).toBe(sampleReceiptText);
    });
  });
});
