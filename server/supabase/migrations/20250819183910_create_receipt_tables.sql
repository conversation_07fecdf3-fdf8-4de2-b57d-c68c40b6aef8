-- Create receipts table to store receipt metadata and store information
CREATE TABLE receipts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Store information
    store_name TEXT NOT NULL,
    store_address TEXT,
    store_city TEXT,
    store_state TEXT,
    store_zip_code TEXT,
    store_phone_number TEXT,

    -- Purchase metadata
    purchase_date DATE NOT NULL,
    purchase_time TIME,
    ticket_number TEXT,

    -- Financial totals
    purchase_total DECIMAL(10,2) NOT NULL,
    purchase_tax DECIMAL(10,2),

    -- Payment information
    credit_card_last_4 TEXT,
    payment_method TEXT,

    -- Raw data for debugging/reprocessing
    raw_pdf_text TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create receipt_items table to store individual line items from receipts
CREATE TABLE receipt_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    receipt_id UUID NOT NULL REFERENCES receipts(id) ON DELETE CASCADE,

    -- Item details
    item_name TEXT NOT NULL,
    item_price DECIMAL(10,2) NOT NULL,
    item_quantity DECIMAL(10,3) DEFAULT 1, -- Support fractional quantities (e.g., 1.5 lbs)
    item_total_price DECIMAL(10,2) NOT NULL,

    -- Preserve order from receipt
    line_order INTEGER NOT NULL,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create master_items table for future item matching/normalization
CREATE TABLE master_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    normalized_name TEXT NOT NULL UNIQUE,
    category TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create item_matches table to track relationships between receipt items and master items
CREATE TABLE item_matches (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    receipt_item_id UUID NOT NULL REFERENCES receipt_items(id) ON DELETE CASCADE,
    master_item_id UUID NOT NULL REFERENCES master_items(id) ON DELETE CASCADE,

    -- Matching metadata
    confidence_score DECIMAL(3,2) DEFAULT 1.0, -- 0.00 to 1.00
    match_method TEXT DEFAULT 'exact', -- 'exact', 'ai', 'manual', 'fuzzy'

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure one-to-one relationship for MVP
    UNIQUE(receipt_item_id)
);

-- Create indexes for efficient querying
CREATE INDEX idx_receipts_user_id ON receipts(user_id);
CREATE INDEX idx_receipts_purchase_date ON receipts(purchase_date);
CREATE INDEX idx_receipts_store_name ON receipts(store_name);
CREATE INDEX idx_receipts_user_date ON receipts(user_id, purchase_date);

CREATE INDEX idx_receipt_items_receipt_id ON receipt_items(receipt_id);
CREATE INDEX idx_receipt_items_name ON receipt_items(item_name);
CREATE INDEX idx_receipt_items_price ON receipt_items(item_price);

CREATE INDEX idx_master_items_name ON master_items(normalized_name);

CREATE INDEX idx_item_matches_receipt_item ON item_matches(receipt_item_id);
CREATE INDEX idx_item_matches_master_item ON item_matches(master_item_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_receipts_updated_at BEFORE UPDATE ON receipts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_receipt_items_updated_at BEFORE UPDATE ON receipt_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_items_updated_at BEFORE UPDATE ON master_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_item_matches_updated_at BEFORE UPDATE ON item_matches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) for multi-user support
ALTER TABLE receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE receipt_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE item_matches ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for receipts (users can only access their own receipts)
CREATE POLICY "Users can view their own receipts" ON receipts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own receipts" ON receipts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own receipts" ON receipts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own receipts" ON receipts
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for receipt_items (access through receipt ownership)
CREATE POLICY "Users can view receipt items for their receipts" ON receipt_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM receipts
            WHERE receipts.id = receipt_items.receipt_id
            AND receipts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert receipt items for their receipts" ON receipt_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM receipts
            WHERE receipts.id = receipt_items.receipt_id
            AND receipts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update receipt items for their receipts" ON receipt_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM receipts
            WHERE receipts.id = receipt_items.receipt_id
            AND receipts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete receipt items for their receipts" ON receipt_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM receipts
            WHERE receipts.id = receipt_items.receipt_id
            AND receipts.user_id = auth.uid()
        )
    );

-- Master items and item matches are shared across users for now
-- but we can add user-specific policies later if needed
CREATE POLICY "Anyone can view master items" ON master_items FOR SELECT USING (true);
CREATE POLICY "Anyone can insert master items" ON master_items FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view item matches for their receipt items" ON item_matches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM receipt_items
            JOIN receipts ON receipts.id = receipt_items.receipt_id
            WHERE receipt_items.id = item_matches.receipt_item_id
            AND receipts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert item matches for their receipt items" ON item_matches
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM receipt_items
            JOIN receipts ON receipts.id = receipt_items.receipt_id
            WHERE receipt_items.id = item_matches.receipt_item_id
            AND receipts.user_id = auth.uid()
        )
    );