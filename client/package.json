{"name": "client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.85.3", "@tanstack/react-router": "^1.131.22", "@tanstack/react-router-devtools": "^1.131.22", "react": "^19.1.0", "react-dom": "^19.1.0", "server": "workspace:*", "shared": "workspace:*", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.28.0", "@tanstack/router-plugin": "^1.131.22", "@types/node": "^22.15.31", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.7.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5"}}