export type ApiResponse = {
  message: string;
  success: true;
};

// Database entity types
export interface Receipt {
  id: string;
  user_id: string | null;

  // Store information
  store_name: string;
  store_address: string | null;
  store_city: string | null;
  store_state: string | null;
  store_zip_code: string | null;
  store_phone_number: string | null;

  // Purchase metadata
  purchase_date: string; // ISO date string
  purchase_time: string | null; // HH:MM:SS format
  ticket_number: string | null;

  // Financial totals
  purchase_total: number;
  purchase_tax: number | null;

  // Payment information
  credit_card_last_4: string | null;
  payment_method: string | null;

  // Raw data
  raw_pdf_text: string | null;

  // Timestamps
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface ReceiptItem {
  id: string;
  receipt_id: string;

  // Item details
  item_name: string;
  item_price: number;
  item_quantity: number;
  item_total_price: number;

  // Order preservation
  line_order: number;

  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface MasterItem {
  id: string;
  normalized_name: string;
  category: string | null;

  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface ItemMatch {
  id: string;
  receipt_item_id: string;
  master_item_id: string;

  // Matching metadata
  confidence_score: number; // 0.00 to 1.00
  match_method: "exact" | "ai" | "manual" | "fuzzy";

  // Timestamps
  created_at: string;
  updated_at: string;
}

// Input types for creating new records
export interface CreateReceiptInput {
  user_id?: string; // Optional for now, can be set server-side

  // Store information
  store_name: string;
  store_address?: string;
  store_city?: string;
  store_state?: string;
  store_zip_code?: string;
  store_phone_number?: string;

  // Purchase metadata
  purchase_date: string; // ISO date string
  purchase_time?: string;
  ticket_number?: string;

  // Financial totals
  purchase_total: number;
  purchase_tax?: number;

  // Payment information
  credit_card_last_4?: string;
  payment_method?: string;

  // Raw data
  raw_pdf_text?: string;
}

export interface CreateReceiptItemInput {
  receipt_id: string;
  item_name: string;
  item_price: number;
  item_quantity: number;
  item_total_price: number;
  line_order: number;
}

// Extended types with related data
export interface ReceiptWithItems extends Receipt {
  items: ReceiptItem[];
}

export interface ReceiptItemWithMasterItem extends ReceiptItem {
  master_item?: MasterItem;
  item_match?: ItemMatch;
}

// Parsed receipt data from PDF processing
export interface ParsedReceiptData {
  // Store information
  store_name: string;
  store_address?: string;
  store_city?: string;
  store_state?: string;
  store_zip_code?: string;
  store_phone_number?: string;

  // Purchase metadata
  purchase_date: string;
  purchase_time?: string;
  ticket_number?: string;

  // Financial totals
  purchase_total: number;
  purchase_tax?: number;

  // Payment information
  credit_card_last_4?: string;
  payment_method?: string;

  // Line items
  items: Array<{
    item_name: string;
    item_price: number;
    item_quantity: number;
    item_total_price: number;
    line_order: number;
  }>;

  // Raw text for debugging
  raw_pdf_text: string;
}

// Analytics response types
export interface MonthlySpendingData {
  month: string; // YYYY-MM format
  total_spent: number;
  receipt_count: number;
  average_per_receipt: number;
}

export interface ItemPriceHistory {
  item_name: string;
  price_points: Array<{
    date: string; // ISO date
    price: number;
    store_name: string;
    receipt_id: string;
  }>;
  average_price: number;
  min_price: number;
  max_price: number;
  price_trend: "increasing" | "decreasing" | "stable";
}

export interface StoreSpendingData {
  store_name: string;
  total_spent: number;
  receipt_count: number;
  average_per_receipt: number;
  first_visit: string; // ISO date
  last_visit: string; // ISO date
}

export interface ExpensiveItemData {
  item_name: string;
  highest_price: number;
  average_price: number;
  purchase_count: number;
  total_spent: number;
  stores: string[]; // List of stores where this item was purchased
}

// API Response types
export interface ReceiptProcessingResponse {
  success: boolean;
  receipt?: Receipt;
  items?: ReceiptItem[];
  parsing_errors?: string[];
  message: string;
}

export interface AnalyticsResponse<T> {
  success: boolean;
  data: T;
  period?: {
    start_date: string;
    end_date: string;
  };
  message: string;
}

// Query parameter types for analytics endpoints
export interface AnalyticsQueryParams {
  start_date?: string; // ISO date
  end_date?: string; // ISO date
  store_name?: string;
  user_id?: string;
}

export interface ItemPriceQueryParams extends AnalyticsQueryParams {
  item_name: string;
}

// Error response type
export interface ErrorResponse {
  success: false;
  error: string;
  details?: any;
}
