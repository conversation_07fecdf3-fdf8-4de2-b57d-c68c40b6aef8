---
type: "always_apply"
---

- This codebase is split in server and client codebases
- For the backend, we'll utilize Supabase. Since all the Supabase code will stay in the server side, we will only use Supabase in the server. Client code won't have any reference to Supabase, other than any cookies that might be necessary to give logged users access to their data (which any way will likely be done through the server.
- Changes to the DB will always be done through local migration files that I will later push to production. You will never make direct changes to the production DB, even if you have access to it.
- The database Typescript types will only ever be generated through the gen:types script that is available in the root directory. No DB types will ever be hardcoded; if there is a need to extract a particular type from the DB, you will only use Typescript tools such as Pick<>, Omit<> or others to construct said type.
